/**
 * 专门针对AutoJS应用的Frida抓包脚本
 * 基于项目中发现的OkHttp和其他网络组件进行定制
 */

console.log("[*] 加载AutoJS专用网络抓包脚本...");

// 工具函数
function bytesToString(bytes) {
    try {
        if (bytes == null) return "";
        const String = Java.use("java.lang.String");
        return String.$new(bytes);
    } catch (e) {
        return "[无法转换字节数据]";
    }
}

function formatJson(jsonString) {
    try {
        const obj = JSON.parse(jsonString);
        return JSON.stringify(obj, null, 2);
    } catch (e) {
        return jsonString;
    }
}

// 保存请求到文件
function saveToFile(data, filename) {
    try {
        const File = Java.use("java.io.File");
        const FileWriter = Java.use("java.io.FileWriter");
        
        const file = File.$new("/sdcard/frida_logs/" + filename);
        const parentDir = file.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        const writer = FileWriter.$new(file, true); // append mode
        writer.write(data + "\n\n");
        writer.close();
        
        console.log(`[💾] 数据已保存到: ${file.getAbsolutePath()}`);
    } catch (e) {
        console.log(`[!] 保存文件失败: ${e}`);
    }
}

// Hook AutoJS中的OkHttp
function hookAutoJSOkHttp() {
    try {
        // Hook OkHttp的内部类（基于你的项目结构）
        const packages = [
            "okhttp3.internal.p042io",
            "okhttp3",
            "org.autojs.autojs"
        ];
        
        // 尝试Hook不同的OkHttp类
        packages.forEach(pkg => {
            try {
                // Hook Request
                const Request = Java.use(`${pkg}.Request`);
                if (Request) {
                    console.log(`[✓] 找到Request类: ${pkg}.Request`);
                }
            } catch (e) {
                // 忽略找不到的类
            }
            
            try {
                // Hook Response
                const Response = Java.use(`${pkg}.Response`);
                if (Response) {
                    console.log(`[✓] 找到Response类: ${pkg}.Response`);
                }
            } catch (e) {
                // 忽略找不到的类
            }
        });
        
        // Hook标准OkHttp3
        const OkHttpClient = Java.use("okhttp3.OkHttpClient");
        const Request = Java.use("okhttp3.Request");
        const Call = Java.use("okhttp3.Call");
        
        // Hook Call.execute()
        const RealCall = Java.use("okhttp3.internal.RealCall");
        RealCall.execute.implementation = function() {
            const request = this.request();
            
            try {
                const method = request.method();
                const url = request.url().toString();
                const timestamp = new Date().toISOString();
                
                let logData = `\n${'='.repeat(100)}\n`;
                logData += `🕐 时间: ${timestamp}\n`;
                logData += `🌐 ${method} ${url}\n`;
                logData += `${'='.repeat(100)}\n`;
                
                // 获取请求头
                const headers = request.headers();
                if (headers.size() > 0) {
                    logData += `📋 请求头:\n`;
                    for (let i = 0; i < headers.size(); i++) {
                        const name = headers.name(i);
                        const value = headers.value(i);
                        logData += `   ${name}: ${value}\n`;
                    }
                }
                
                // 获取请求体
                const requestBody = request.body();
                if (requestBody != null) {
                    try {
                        const Buffer = Java.use("okio.Buffer");
                        const buffer = Buffer.$new();
                        requestBody.writeTo(buffer);
                        const bodyString = buffer.readUtf8();
                        
                        logData += `📦 请求体:\n`;
                        if (bodyString.startsWith('{') || bodyString.startsWith('[')) {
                            logData += formatJson(bodyString) + '\n';
                        } else {
                            logData += bodyString + '\n';
                        }
                    } catch (e) {
                        logData += `📦 请求体: [无法读取 - ${e}]\n`;
                    }
                }
                
                console.log(logData);
                saveToFile(logData, `autojs_requests_${new Date().toISOString().split('T')[0]}.log`);
                
            } catch (e) {
                console.log(`[!] Hook Request 错误: ${e}`);
            }
            
            // 执行原始请求
            const response = this.execute();
            
            // Hook响应
            try {
                const responseBody = response.body();
                if (responseBody != null) {
                    const responseString = responseBody.string();
                    
                    let responseLog = `📥 响应状态: ${response.code()} ${response.message()}\n`;
                    responseLog += `📥 响应体:\n`;
                    
                    if (responseString.startsWith('{') || responseString.startsWith('[')) {
                        responseLog += formatJson(responseString);
                    } else {
                        responseLog += responseString;
                    }
                    
                    console.log(responseLog);
                    saveToFile(responseLog, `autojs_responses_${new Date().toISOString().split('T')[0]}.log`);
                    
                    // 重新创建ResponseBody，因为已经被读取了
                    const MediaType = Java.use("okhttp3.MediaType");
                    const ResponseBody = Java.use("okhttp3.ResponseBody");
                    const newBody = ResponseBody.create(responseBody.contentType(), responseString);
                    
                    // 创建新的Response
                    const newResponse = response.newBuilder().body(newBody).build();
                    return newResponse;
                }
            } catch (e) {
                console.log(`[!] Hook Response 错误: ${e}`);
            }
            
            return response;
        };
        
        console.log("[✓] AutoJS OkHttp Hook 设置完成");
        
    } catch (e) {
        console.log(`[!] AutoJS OkHttp Hook 失败: ${e}`);
    }
}

// Hook JavaScript引擎中的网络请求
function hookJavaScriptEngine() {
    try {
        // Hook Rhino JavaScript引擎（AutoJS使用的）
        const Context = Java.use("org.mozilla.javascript.Context");
        const ScriptRuntime = Java.use("org.mozilla.javascript.ScriptRuntime");
        
        console.log("[✓] 找到JavaScript引擎相关类");
        
        // 可以在这里添加更多JavaScript引擎相关的Hook
        
    } catch (e) {
        console.log(`[!] JavaScript引擎Hook失败: ${e}`);
    }
}

// Hook AutoJS特定的网络组件
function hookAutoJSSpecific() {
    try {
        // 基于项目结构Hook特定的类
        const classesToHook = [
            "org.autojs.autojs.App",
            "org.autojs.autojs.autojs.AutoJs",
            "org.autojs.autojs.ui.main.MainActivity"
        ];
        
        classesToHook.forEach(className => {
            try {
                const clazz = Java.use(className);
                console.log(`[✓] 找到AutoJS类: ${className}`);
                
                // 可以在这里添加特定的方法Hook
                
            } catch (e) {
                // 忽略找不到的类
            }
        });
        
    } catch (e) {
        console.log(`[!] AutoJS特定Hook失败: ${e}`);
    }
}

// Hook文件操作（可能涉及网络下载）
function hookFileOperations() {
    try {
        const FileInputStream = Java.use("java.io.FileInputStream");
        const FileOutputStream = Java.use("java.io.FileOutputStream");
        const URL = Java.use("java.net.URL");
        
        // Hook URL连接
        URL.openConnection.overload().implementation = function() {
            console.log(`[🔗] URL连接: ${this.toString()}`);
            return this.openConnection();
        };
        
        console.log("[✓] 文件操作Hook设置完成");
        
    } catch (e) {
        console.log(`[!] 文件操作Hook失败: ${e}`);
    }
}

// 主函数
function main() {
    Java.perform(function() {
        console.log("🚀 开始Hook AutoJS网络请求...");
        
        // 等待应用完全加载
        setTimeout(function() {
            hookAutoJSOkHttp();
            hookJavaScriptEngine();
            hookAutoJSSpecific();
            hookFileOperations();
            
            console.log("\n✅ AutoJS专用Hook设置完成！");
            console.log("📁 日志文件保存在: /sdcard/frida_logs/");
            console.log("🔍 开始监控网络活动...\n");
            
        }, 2000);
    });
}

// 启动脚本
main();
