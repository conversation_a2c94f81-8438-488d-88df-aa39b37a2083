# Frida 抓包脚本使用说明

## 文件说明

1. **frida_network_hook.js** - 通用网络抓包脚本
   - 支持 OkHttp3、HttpURLConnection、WebView、Socket 等
   - 包含 SSL/TLS 证书绕过
   - 彩色输出，便于查看

2. **frida_autojs_hook.js** - AutoJS 专用抓包脚本
   - 针对 AutoJS 项目定制
   - 支持日志保存到文件
   - Hook JavaScript 引擎相关网络请求

## 使用方法

### 1. 安装 Frida

```bash
# 安装 Frida
pip install frida-tools

# 或者使用 npm
npm install -g frida-tools
```

### 2. 准备设备

确保你的 Android 设备已经：
- 开启开发者选项和 USB 调试
- 安装了 Frida Server（需要 root 权限）

### 3. 启动 Frida Server

在设备上启动 Frida Server：
```bash
adb shell
su
cd /data/local/tmp
./frida-server &
```

### 4. 运行抓包脚本

#### 方法一：使用通用脚本
```bash
# 获取应用包名
frida-ps -U

# 运行脚本（替换 com.example.app 为实际包名）
frida -U -f com.example.app -l frida_network_hook.js --no-pause
```

#### 方法二：使用 AutoJS 专用脚本
```bash
# 针对 AutoJS 应用
frida -U -f org.autojs.autojspro -l frida_autojs_hook.js --no-pause
```

#### 方法三：附加到正在运行的进程
```bash
# 先启动应用，然后附加
frida -U org.autojs.autojspro -l frida_network_hook.js
```

## 脚本功能

### 通用脚本功能
- ✅ Hook OkHttp3 请求和响应
- ✅ Hook HttpURLConnection
- ✅ Hook WebView 网络请求
- ✅ Hook Socket 连接
- ✅ SSL/TLS 证书绕过
- ✅ 彩色控制台输出
- ✅ 详细的请求头和请求体显示

### AutoJS 专用脚本功能
- ✅ 针对 AutoJS 项目结构优化
- ✅ 自动保存日志到文件 (`/sdcard/frida_logs/`)
- ✅ JSON 格式化输出
- ✅ 时间戳记录
- ✅ Hook JavaScript 引擎网络请求
- ✅ 文件操作监控

## 输出示例

```
================================================================================
🌐 POST https://api.example.com/login
================================================================================
📋 Headers:
   Content-Type: application/json
   User-Agent: AutoJS/1.0
   Authorization: Bearer token123
📦 Body:
{
  "username": "user",
  "password": "pass"
}
================================================================================

📥 Response Body:
{
  "status": "success",
  "token": "new_token_here"
}
```

## 常见问题

### 1. 连接失败
- 确保 Frida Server 正在运行
- 检查 USB 调试是否开启
- 确认设备已 root

### 2. 找不到应用
```bash
# 列出所有应用
frida-ps -U

# 列出正在运行的应用
frida-ps -Ua
```

### 3. 权限问题
- 确保 Frida Server 有 root 权限
- 检查 `/sdcard/frida_logs/` 目录权限

### 4. 脚本报错
- 检查目标应用是否使用了对应的网络库
- 某些混淆的应用可能需要调整类名

## 高级用法

### 1. 过滤特定 URL
在脚本中添加过滤条件：
```javascript
if (url.includes("api.target.com")) {
    // 只记录特定域名的请求
}
```

### 2. 修改请求
```javascript
// 修改请求头
headers["Custom-Header"] = "Modified";

// 修改请求体
body = body.replace("old_value", "new_value");
```

### 3. 保存到不同文件
```javascript
// 根据 URL 分类保存
if (url.includes("login")) {
    saveToFile(data, "login_requests.log");
} else if (url.includes("api")) {
    saveToFile(data, "api_requests.log");
}
```

## 注意事项

1. **合法使用**：仅用于测试自己的应用或获得授权的应用
2. **性能影响**：Hook 可能会影响应用性能
3. **日志大小**：长时间运行可能产生大量日志文件
4. **隐私保护**：注意保护敏感信息，如密码、token 等

## 故障排除

如果遇到问题，可以：
1. 检查 Frida 版本兼容性
2. 查看设备日志：`adb logcat | grep frida`
3. 尝试不同的 Hook 点
4. 联系开发者获取支持
