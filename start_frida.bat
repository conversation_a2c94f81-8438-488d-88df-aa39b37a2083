@echo off
chcp 65001 >nul
echo ========================================
echo        Frida 抓包脚本启动器
echo ========================================
echo.

:menu
echo 请选择要使用的脚本:
echo 1. 通用网络抓包脚本 (frida_network_hook.js)
echo 2. AutoJS专用抓包脚本 (frida_autojs_hook.js)
echo 3. 查看设备上的应用列表
echo 4. 查看正在运行的应用
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto general_hook
if "%choice%"=="2" goto autojs_hook
if "%choice%"=="3" goto list_apps
if "%choice%"=="4" goto list_running
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:general_hook
echo.
echo 启动通用网络抓包脚本...
echo.
set /p package=请输入应用包名 (例如: com.example.app): 
if "%package%"=="" (
    echo 包名不能为空
    goto menu
)
echo.
echo 正在启动 Frida...
frida -U -f %package% -l frida_network_hook.js --no-pause
pause
goto menu

:autojs_hook
echo.
echo 启动AutoJS专用抓包脚本...
echo.
echo 常见的AutoJS包名:
echo - org.autojs.autojspro
echo - org.autojs.autojs
echo - com.stardust.autojs
echo.
set /p package=请输入AutoJS应用包名: 
if "%package%"=="" (
    set package=org.autojs.autojspro
    echo 使用默认包名: org.autojs.autojspro
)
echo.
echo 正在启动 Frida...
frida -U -f %package% -l frida_autojs_hook.js --no-pause
pause
goto menu

:list_apps
echo.
echo 获取设备上的应用列表...
frida-ps -U
echo.
pause
goto menu

:list_running
echo.
echo 获取正在运行的应用列表...
frida-ps -Ua
echo.
pause
goto menu

:exit
echo 再见！
exit /b 0
