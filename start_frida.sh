#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "       Frida 抓包脚本启动器"
echo -e "========================================${NC}"
echo

show_menu() {
    echo -e "${YELLOW}请选择要使用的脚本:${NC}"
    echo "1. 通用网络抓包脚本 (frida_network_hook.js)"
    echo "2. AutoJS专用抓包脚本 (frida_autojs_hook.js)"
    echo "3. 查看设备上的应用列表"
    echo "4. 查看正在运行的应用"
    echo "5. 附加到正在运行的进程"
    echo "6. 退出"
    echo
}

general_hook() {
    echo
    echo -e "${GREEN}启动通用网络抓包脚本...${NC}"
    echo
    read -p "请输入应用包名 (例如: com.example.app): " package
    if [ -z "$package" ]; then
        echo -e "${RED}包名不能为空${NC}"
        return
    fi
    echo
    echo -e "${BLUE}正在启动 Frida...${NC}"
    frida -U -f "$package" -l frida_network_hook.js --no-pause
}

autojs_hook() {
    echo
    echo -e "${GREEN}启动AutoJS专用抓包脚本...${NC}"
    echo
    echo -e "${YELLOW}常见的AutoJS包名:${NC}"
    echo "- org.autojs.autojspro"
    echo "- org.autojs.autojs"
    echo "- com.stardust.autojs"
    echo
    read -p "请输入AutoJS应用包名 (默认: org.autojs.autojspro): " package
    if [ -z "$package" ]; then
        package="org.autojs.autojspro"
        echo -e "${YELLOW}使用默认包名: org.autojs.autojspro${NC}"
    fi
    echo
    echo -e "${BLUE}正在启动 Frida...${NC}"
    frida -U -f "$package" -l frida_autojs_hook.js --no-pause
}

list_apps() {
    echo
    echo -e "${GREEN}获取设备上的应用列表...${NC}"
    frida-ps -U
    echo
}

list_running() {
    echo
    echo -e "${GREEN}获取正在运行的应用列表...${NC}"
    frida-ps -Ua
    echo
}

attach_process() {
    echo
    echo -e "${GREEN}附加到正在运行的进程...${NC}"
    echo
    echo -e "${YELLOW}正在运行的应用:${NC}"
    frida-ps -Ua
    echo
    read -p "请输入要附加的应用包名: " package
    if [ -z "$package" ]; then
        echo -e "${RED}包名不能为空${NC}"
        return
    fi
    
    echo
    echo -e "${YELLOW}选择脚本:${NC}"
    echo "1. 通用脚本"
    echo "2. AutoJS专用脚本"
    read -p "请选择 (1-2): " script_choice
    
    case $script_choice in
        1)
            echo -e "${BLUE}使用通用脚本附加到 $package...${NC}"
            frida -U "$package" -l frida_network_hook.js
            ;;
        2)
            echo -e "${BLUE}使用AutoJS专用脚本附加到 $package...${NC}"
            frida -U "$package" -l frida_autojs_hook.js
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            ;;
    esac
}

# 检查Frida是否安装
if ! command -v frida &> /dev/null; then
    echo -e "${RED}错误: 未找到 Frida 工具${NC}"
    echo "请先安装 Frida: pip install frida-tools"
    exit 1
fi

# 检查设备连接
if ! frida-ps -U &> /dev/null; then
    echo -e "${RED}错误: 无法连接到设备${NC}"
    echo "请确保:"
    echo "1. 设备已连接并开启USB调试"
    echo "2. Frida Server正在设备上运行"
    echo "3. 设备已获得root权限"
    exit 1
fi

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            general_hook
            ;;
        2)
            autojs_hook
            ;;
        3)
            list_apps
            ;;
        4)
            list_running
            ;;
        5)
            attach_process
            ;;
        6)
            echo -e "${GREEN}再见！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选择，请重新输入${NC}"
            ;;
    esac
    
    echo
    read -p "按回车键继续..."
    echo
done
