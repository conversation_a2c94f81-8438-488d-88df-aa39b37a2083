/**
 * Frida网络抓包脚本
 * 用于hook Android应用的网络请求
 * 支持OkHttp、HttpURLConnection、Volley等常见网络库
 */

console.log("[*] 开始加载Frida网络抓包脚本...");

// 颜色输出函数
function colorLog(message, color = 'white') {
    const colors = {
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m',
        white: '\x1b[37m',
        reset: '\x1b[0m'
    };
    console.log(colors[color] + message + colors.reset);
}

// 格式化输出函数
function formatRequest(method, url, headers, body) {
    let output = `\n${'='.repeat(80)}\n`;
    output += `🌐 ${method} ${url}\n`;
    output += `${'='.repeat(80)}\n`;
    
    if (headers && Object.keys(headers).length > 0) {
        output += `📋 Headers:\n`;
        for (let key in headers) {
            output += `   ${key}: ${headers[key]}\n`;
        }
    }
    
    if (body) {
        output += `📦 Body:\n${body}\n`;
    }
    
    output += `${'='.repeat(80)}`;
    return output;
}

// Hook OkHttp3
function hookOkHttp3() {
    try {
        // Hook OkHttp3 Request
        const Request = Java.use("okhttp3.Request");
        const RequestBody = Java.use("okhttp3.RequestBody");
        const Buffer = Java.use("okio.Buffer");
        
        // Hook Request.Builder.build()
        const RequestBuilder = Java.use("okhttp3.Request$Builder");
        RequestBuilder.build.implementation = function() {
            const request = this.build();
            
            try {
                const method = request.method();
                const url = request.url().toString();
                const headers = {};
                
                const headerNames = request.headers().names();
                const headerNamesArray = headerNames.toArray();
                
                for (let i = 0; i < headerNamesArray.length; i++) {
                    const name = headerNamesArray[i];
                    headers[name] = request.headers().get(name);
                }
                
                let body = "";
                const requestBody = request.body();
                if (requestBody != null) {
                    try {
                        const buffer = Buffer.$new();
                        requestBody.writeTo(buffer);
                        body = buffer.readUtf8();
                    } catch (e) {
                        body = "[无法读取请求体]";
                    }
                }
                
                colorLog(formatRequest(method, url, headers, body), 'cyan');
                
            } catch (e) {
                colorLog(`[!] OkHttp Request Hook 错误: ${e}`, 'red');
            }
            
            return request;
        };
        
        // Hook Response
        const Response = Java.use("okhttp3.Response");
        const ResponseBody = Java.use("okhttp3.ResponseBody");
        
        ResponseBody.string.implementation = function() {
            const responseString = this.string();
            
            try {
                colorLog(`\n📥 Response Body:\n${responseString}`, 'green');
            } catch (e) {
                colorLog(`[!] Response Hook 错误: ${e}`, 'red');
            }
            
            return responseString;
        };
        
        colorLog("[✓] OkHttp3 Hook 成功", 'green');
        
    } catch (e) {
        colorLog(`[!] OkHttp3 Hook 失败: ${e}`, 'red');
    }
}

// Hook HttpURLConnection
function hookHttpURLConnection() {
    try {
        const HttpURLConnection = Java.use("java.net.HttpURLConnection");
        const URL = Java.use("java.net.URL");
        
        // Hook connect方法
        HttpURLConnection.connect.implementation = function() {
            try {
                const url = this.getURL().toString();
                const method = this.getRequestMethod();
                
                colorLog(`\n🔗 HttpURLConnection: ${method} ${url}`, 'yellow');
                
                // 获取请求头
                const headerFields = this.getRequestProperties();
                if (headerFields != null) {
                    const headers = {};
                    const keySet = headerFields.keySet();
                    const iterator = keySet.iterator();
                    
                    while (iterator.hasNext()) {
                        const key = iterator.next();
                        const values = headerFields.get(key);
                        if (values != null && values.size() > 0) {
                            headers[key] = values.get(0);
                        }
                    }
                    
                    if (Object.keys(headers).length > 0) {
                        colorLog("📋 Headers:", 'yellow');
                        for (let key in headers) {
                            colorLog(`   ${key}: ${headers[key]}`, 'yellow');
                        }
                    }
                }
                
            } catch (e) {
                colorLog(`[!] HttpURLConnection Hook 错误: ${e}`, 'red');
            }
            
            return this.connect();
        };
        
        colorLog("[✓] HttpURLConnection Hook 成功", 'green');
        
    } catch (e) {
        colorLog(`[!] HttpURLConnection Hook 失败: ${e}`, 'red');
    }
}

// Hook WebView
function hookWebView() {
    try {
        const WebView = Java.use("android.webkit.WebView");
        
        // Hook loadUrl
        WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
            colorLog(`\n🌐 WebView loadUrl: ${url}`, 'magenta');
            return this.loadUrl(url);
        };
        
        // Hook loadUrl with headers
        WebView.loadUrl.overload('java.lang.String', 'java.util.Map').implementation = function(url, headers) {
            colorLog(`\n🌐 WebView loadUrl with headers: ${url}`, 'magenta');
            
            if (headers != null) {
                const keySet = headers.keySet();
                const iterator = keySet.iterator();
                
                colorLog("📋 Headers:", 'magenta');
                while (iterator.hasNext()) {
                    const key = iterator.next();
                    const value = headers.get(key);
                    colorLog(`   ${key}: ${value}`, 'magenta');
                }
            }
            
            return this.loadUrl(url, headers);
        };
        
        colorLog("[✓] WebView Hook 成功", 'green');
        
    } catch (e) {
        colorLog(`[!] WebView Hook 失败: ${e}`, 'red');
    }
}

// Hook Socket连接
function hookSocket() {
    try {
        const Socket = Java.use("java.net.Socket");
        
        // Hook connect方法
        Socket.connect.overload('java.net.SocketAddress').implementation = function(endpoint) {
            colorLog(`\n🔌 Socket连接: ${endpoint.toString()}`, 'blue');
            return this.connect(endpoint);
        };
        
        Socket.connect.overload('java.net.SocketAddress', 'int').implementation = function(endpoint, timeout) {
            colorLog(`\n🔌 Socket连接 (超时${timeout}ms): ${endpoint.toString()}`, 'blue');
            return this.connect(endpoint, timeout);
        };
        
        colorLog("[✓] Socket Hook 成功", 'green');
        
    } catch (e) {
        colorLog(`[!] Socket Hook 失败: ${e}`, 'red');
    }
}

// Hook SSL/TLS
function hookSSL() {
    try {
        // Hook SSLContext
        const SSLContext = Java.use("javax.net.ssl.SSLContext");
        const TrustManager = Java.use("javax.net.ssl.X509TrustManager");
        const SSLSocketFactory = Java.use("javax.net.ssl.SSLSocketFactory");
        
        // 创建信任所有证书的TrustManager
        const TrustAllManager = Java.registerClass({
            name: 'org.frida.TrustAllManager',
            implements: [TrustManager],
            methods: {
                checkClientTrusted: function(chain, authType) {
                    colorLog("[🔒] SSL: 跳过客户端证书验证", 'yellow');
                },
                checkServerTrusted: function(chain, authType) {
                    colorLog("[🔒] SSL: 跳过服务器证书验证", 'yellow');
                },
                getAcceptedIssuers: function() {
                    return [];
                }
            }
        });
        
        // Hook SSLContext.init
        SSLContext.init.implementation = function(keyManagers, trustManagers, secureRandom) {
            colorLog("[🔒] SSL: 使用自定义TrustManager", 'yellow');
            const trustAllManager = TrustAllManager.$new();
            return this.init(keyManagers, [trustAllManager], secureRandom);
        };
        
        colorLog("[✓] SSL Hook 成功", 'green');
        
    } catch (e) {
        colorLog(`[!] SSL Hook 失败: ${e}`, 'red');
    }
}

// 主函数
function main() {
    Java.perform(function() {
        colorLog("🚀 开始Hook网络请求...", 'cyan');
        
        // 延迟执行，确保应用完全启动
        setTimeout(function() {
            hookOkHttp3();
            hookHttpURLConnection();
            hookWebView();
            hookSocket();
            hookSSL();
            
            colorLog("\n✅ 所有Hook已设置完成，开始监控网络请求...", 'green');
        }, 1000);
    });
}

// 启动脚本
main();
